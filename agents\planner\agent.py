import asyncio
import os
from dotenv import load_dotenv
from google.adk.agents import Agent
from google.adk.tools.mcp_tool.mcp_toolset import MCPToolset, StdioServerParameters


# Load environment variables from the project root .env file
load_dotenv(dotenv_path=os.path.join(os.path.dirname(__file__), '..', '..', '.env'))

async def create_agent():
    """Creates the Task Planner agent by connecting to the ai_knowledge_bridge MCP server."""
    print("--- Attempting to start and connect to ai_knowledge_bridge MCP server ---")

    tools, exit_stack = await MCPToolset.from_server(
        connection_params=StdioServerParameters(
            command='python',
            args=['c:/dev/MCP/ai__information_share/run_ai_knowledge_bridge.py'],
            env=os.environ.copy()
        )
    )

    print(f"--- Connected to ai_knowledge_bridge. Discovered {len(tools)} tool(s). ---")
    for tool in tools:
        print(f"  - Discovered tool: {tool.name}")

    # Create the Task Planner agent (following existing system pattern)
    agent_instance = Agent(
        name="task_planner_agent",
        description="Processes user input through web interface and splits into task planning and check indicators.",
        model="gemini-1.5-flash-latest",
        instruction=(
            "You are a Task Planner agent. Your role is to:\n\n"
            "1. **Process User Input**: Take user requests and send them to the web interface using ai_knowledge_bridge MCP tools.\n"
            "2. **Parse Web Response**: The web interface will return a response containing both task planning and check indicators.\n"
            "3. **Split Output**: Carefully separate the response into two parts:\n"
            "   - **Task Planning Part**: Detailed step-by-step instructions for task execution\n"
            "   - **Check Indicators Part**: Specific criteria and metrics to verify task completion\n"
            "4. **Format Output**: Return the results in a structured format that clearly identifies:\n"
            "   - TASK_PLANNING: [detailed execution steps]\n"
            "   - CHECK_INDICATORS: [completion verification criteria]\n\n"
            "Always use the ai_knowledge_bridge tools to process user input through the web interface.\n"
            "Be precise in separating task planning from check indicators.\n"
            "Ensure both parts are comprehensive and actionable."
        ),
        tools=tools,
    )

    return agent_instance, exit_stack

# Create the root agent for ADK - following the existing pattern
root_agent = create_agent()
