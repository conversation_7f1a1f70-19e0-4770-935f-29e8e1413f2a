import async<PERSON>
import os
from contextlib import AsyncExitStack
from dotenv import load_dotenv
from google.adk.agents import Agent
from google.adk.tools.mcp_tool.mcp_toolset import MCPToolset, StdioServerParameters


# Load environment variables from the project root .env file
load_dotenv(dotenv_path=os.path.join(os.path.dirname(__file__), '..', '..', '.env'))

async def create_agent():
    """Creates the Task Executor agent by connecting to AutoOmni and desktop-commander MCP servers."""
    print("--- Attempting to start and connect to AutoOmni and desktop-commander MCP servers ---")

    # Manage multiple MCP connections
    exit_stack = AsyncExitStack()
    await exit_stack.__aenter__()

    all_tools = []

    # Connect to AutoOmni MCP server
    try:
        print("--- Connecting to AutoOmni MCP server ---")
        auto_omni_tools, auto_omni_stack = await MCPToolset.from_server(
            connection_params=StdioServerParameters(
                command='python',
                args=['c:/dev/MCP/AutoOmni/auto_omni_mcp.py'],
                env=os.environ.copy()
            )
        )
        await exit_stack.enter_async_context(auto_omni_stack)
        all_tools.extend(auto_omni_tools)
        print(f"--- Connected to AutoOmni. Discovered {len(auto_omni_tools)} tool(s). ---")
        for tool in auto_omni_tools:
            print(f"  - AutoOmni tool: {tool.name}")
    except Exception as e:
        print(f"--- Warning: Failed to connect to AutoOmni: {e} ---")

    # Connect to desktop-commander MCP server
    try:
        print("--- Connecting to desktop-commander MCP server ---")
        desktop_tools, desktop_stack = await MCPToolset.from_server(
            connection_params=StdioServerParameters(
                command='npx',
                args=['-y', '@wonderwhy-er/desktop-commander'],
                env=os.environ.copy()
            )
        )
        await exit_stack.enter_async_context(desktop_stack)
        all_tools.extend(desktop_tools)
        print(f"--- Connected to desktop-commander. Discovered {len(desktop_tools)} tool(s). ---")
        for tool in desktop_tools:
            print(f"  - Desktop tool: {tool.name}")
    except Exception as e:
        print(f"--- Warning: Failed to connect to desktop-commander: {e} ---")

    print(f"--- Total tools available: {len(all_tools)} ---")

    # Create the Task Executor agent (following existing system pattern)
    agent_instance = Agent(
        name="task_executor_agent",
        description="Executes task planning sequentially using terminal commands, desktop operations, and file system operations.",
        model="gemini-1.5-flash-latest",
        instruction=(
            "You are a Task Executor agent. Your role is to:\n\n"
            "1. **Receive Task Planning**: Accept detailed task planning instructions from the Task Planner agent.\n"
            "2. **Sequential Execution**: Execute tasks step by step in the specified order.\n"
            "3. **Use Available Tools**: Utilize the connected MCP tools for:\n"
            "   - Terminal operations (AutoOmni)\n"
            "   - Desktop operations (desktop-commander)\n"
            "   - File system operations\n"
            "4. **Prefer Terminal Commands**: When possible, use terminal commands and keyboard shortcuts for efficiency.\n"
            "5. **Error Handling**: If a step fails, report the error clearly and attempt alternative approaches.\n"
            "6. **Progress Reporting**: Provide clear status updates for each completed step.\n"
            "7. **Information Provision**: When requested by the Checker agent, provide detailed information about:\n"
            "   - Current system state\n"
            "   - Files created/modified\n"
            "   - Commands executed\n"
            "   - Any outputs or results\n\n"
            "Always execute tasks methodically and provide comprehensive feedback.\n"
            "Use the most appropriate tool for each operation type.\n"
            "Prioritize terminal commands and shortcuts for speed and reliability."
        ),
        tools=all_tools,
    )

    return agent_instance, exit_stack

# Create the root agent for ADK - following the existing pattern
root_agent = create_agent()
